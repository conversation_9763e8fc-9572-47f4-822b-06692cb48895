#!/usr/bin/env python3
"""
Simple FastAPI server test to isolate the hanging issue
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create a simple FastAPI app
app = FastAPI(title="Simple Test Server")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    """Simple root endpoint"""
    logger.info("Root endpoint called")
    return {"message": "Simple server is working!"}

@app.get("/health")
async def health():
    """Simple health check"""
    logger.info("Health endpoint called")
    return {"status": "healthy", "message": "Server is responding"}

@app.get("/test")
async def test():
    """Test endpoint"""
    logger.info("Test endpoint called")
    return {"test": "success", "data": [1, 2, 3, 4, 5]}

if __name__ == "__main__":
    logger.info("Starting simple test server on port 8002...")
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8002,
        log_level="info"
    )
