import clickhouse_connect
from typing import Dict, List, Any, Optional
import logging
from app.config import settings

logger = logging.getLogger(__name__)

class Database:
    def __init__(self):
        self.client = None
        self._connection_attempted = False

    def _connect(self):
        """Initialize ClickHouse connection using clickhouse_connect"""
        self._connection_attempted = True
        try:
            # Connection parameters
            connect_params = {
                'host': settings.db_host,
                'port': settings.db_port,
                'username': settings.db_user,
                'password': settings.db_password,
                'database': settings.db_name,
                'secure': settings.db_secure,
                'verify': settings.db_verify,
            }

            # Add SSL certificate parameters if provided
            if settings.db_ca_cert:
                connect_params['ca_cert'] = settings.db_ca_cert
            if settings.db_client_cert:
                connect_params['client_cert'] = settings.db_client_cert
            if settings.db_client_key:
                connect_params['client_key'] = settings.db_client_key

            logger.info(f"Connecting to ClickHouse at {settings.db_host}:{settings.db_port}")
            logger.debug(f"Connection params: {dict(connect_params, password='***')}")

            self.client = clickhouse_connect.get_client(**connect_params)
            logger.info("ClickHouse client initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize ClickHouse client: {e}")
            self.client = None
            # Don't raise here to allow the app to start even if DB is unavailable

    def _ensure_connected(self):
        """Ensure database connection is established"""
        if not self.client and not self._connection_attempted:
            self._connect()
        elif not self.client:
            raise ConnectionError("ClickHouse client failed to initialize")

    def _execute_clickhouse_query(self, query: str, params: Optional[List[Any]] = None) -> List[Dict[str, Any]]:
        """Execute a query against ClickHouse using clickhouse_connect"""
        self._ensure_connected()
        if not self.client:
            raise ConnectionError("ClickHouse client not initialized")

        try:
            logger.debug(f"Executing ClickHouse query: {query[:200]}...")

            if params:
                # Use parameterized queries with clickhouse_connect
                result = self.client.query(query, parameters=params)
            else:
                result = self.client.query(query)

            # Convert result to list of dictionaries
            if result.result_rows:
                columns = result.column_names
                return [dict(zip(columns, row)) for row in result.result_rows]
            else:
                return []

        except Exception as e:
            logger.error(f"ClickHouse query error: {e}")
            raise

    def execute_query(self, query: str, params: Optional[List[Any]] = None) -> List[Dict[str, Any]]:
        """Execute a SELECT query and return results as list of dictionaries"""
        try:
            return self._execute_clickhouse_query(query, params)
        except Exception as e:
            logger.error(f"Database query error: {e}")
            raise

    def execute_single_query(self, query: str, params: Optional[List[Any]] = None) -> Optional[Dict[str, Any]]:
        """Execute a SELECT query and return single result as dictionary"""
        try:
            results = self.execute_query(query, params)
            return results[0] if results else None
        except Exception as e:
            logger.error(f"Database single query error: {e}")
            raise

    def test_connection(self) -> bool:
        """Test database connection"""
        try:
            self._ensure_connected()
            if not self.client:
                logger.error("ClickHouse client not initialized")
                return False

            # Test with a simple query
            result = self.client.query("SELECT 1 as test")
            return len(result.result_rows) > 0
        except Exception as e:
            logger.error(f"Database connection test failed: {e}")
            return False

    def execute_insert(self, table: str, data: List[Dict[str, Any]]) -> bool:
        """Insert data into a table using clickhouse_connect's optimized insert"""
        try:
            if not self.client:
                raise ConnectionError("ClickHouse client not initialized")

            if not data:
                logger.warning("No data provided for insert")
                return True

            # Use clickhouse_connect's insert method for better performance
            self.client.insert(table, data)
            logger.info(f"Successfully inserted {len(data)} rows into {table}")
            return True

        except Exception as e:
            logger.error(f"Insert error for table {table}: {e}")
            raise

    def get_server_info(self) -> Dict[str, Any]:
        """Get ClickHouse server information"""
        try:
            if not self.client:
                raise ConnectionError("ClickHouse client not initialized")

            result = self.client.query("SELECT version() as version, uptime() as uptime")
            if result.result_rows:
                return dict(zip(result.column_names, result.result_rows[0]))
            return {}

        except Exception as e:
            logger.error(f"Failed to get server info: {e}")
            raise

    def close(self):
        """Close the database connection"""
        try:
            if self.client:
                self.client.close()
                logger.info("ClickHouse connection closed")
        except Exception as e:
            logger.error(f"Error closing connection: {e}")

# Global database instance
db = Database()
